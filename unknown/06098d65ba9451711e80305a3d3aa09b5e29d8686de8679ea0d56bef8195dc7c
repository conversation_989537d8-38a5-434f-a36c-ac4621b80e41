// "use client";
// import { useState, useEffect } from "react";
// import styles from "./tradeTimer.module.css";
// import { increaseTradeTime } from "../../api/tradeApis/clockApi";
// import { toast, ToastContainer } from "react-toastify";
// import "react-toastify/dist/ReactToastify.css";
// import { useWebsocketContext } from "@/app/context/AuthContext";

// const Page = ({ duration, orderNumber }) => {
//   const { connection, sendMessage } = useWebsocketContext();

//   // console.log("duration", duration);
//   const [time, setTime] = useState(null);

//   const [stopTimer, setStopTimer] = useState(false);
//   const [showConfirmation, setShowConfirmation] = useState(false);
//   const [timerIncreased, setTimerIncreased] = useState(false);

//   const handleTimeIncrease = async () => {
//     const payload = {
//       action: "increase_trade_time",
//       order_id: orderNumber,
//     };
//     sendMessage(JSON.stringify(payload));
//     setShowConfirmation(false);
//     setTimerIncreased(true);
//     //   fetchTradeTime();
//   };

//   const handleTimeReject = async () => {
//     setShowConfirmation(false);
//   };

//   useEffect(() => {
//     // if (time === 570000 && !timerIncreased) {
//     if (time === 60000 && !timerIncreased) {
//       setShowConfirmation(true);
//     }
//   }, [time, timerIncreased]);

//   useEffect(() => {
//     if (time === 0) {
//       setStopTimer(true);
//     }
//   }, [time]);

//   const getFormattedTime = (milliseconds) => {
//     let totalSeconds = parseInt(Math.floor(milliseconds / 1000));
//     let totalMinutes = parseInt(Math.floor(totalSeconds / 60));

//     let seconds = parseInt(totalSeconds % 60);
//     let minutes = parseInt(totalMinutes % 60);

//     return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;
//   };

//   useEffect(() => {
//     let timer = null;
//     if (duration !== null) {
//       setTime(duration);

//       if (!stopTimer && duration !== undefined) {
//         // console.log("callSetimeout1", time);
//         timer = setInterval(() => {
//           setTime((previous) => previous - 1000);

//           // console.log("callSetimeout", time);
//         }, 1000);
//       }
//     }
//     return () => clearInterval(timer);
//   }, [duration, stopTimer]);

//   useEffect(() => {
//     if (time === 0) {
//       setStopTimer(true);
//     }
//   }, [time]);

//   return (
//     <>
//       <div className={styles.countDownTimer}>
//         {duration === undefined ? "expired" : getFormattedTime(time)}
//       </div>
//       {/* <div className={styles.countDownTimer}>
//         {duration === null || stopTimer
//           ? "Trade expired"
//           : duration !== null
//           ? duration
//           : "Loading..."}
//       </div> */}

//       {showConfirmation && (
//         <div className={styles.alertPopup}>
//           <p>
//             Your order N1Q076: Do you want to increase trade time by 10 minutes?
//           </p>
//           <div className={styles.btnContainer}>
//             <button className={styles.yesBtn} onClick={handleTimeIncrease}>
//               Yes
//             </button>
//             <button className={styles.noBtn} onClick={handleTimeReject}>
//               No
//             </button>
//           </div>
//         </div>
//       )}
//       <ToastContainer />
//     </>
//   );
// };

// export default Page;

"use client";
import { useState, useEffect } from "react";
import styles from "./tradeTimer.module.css";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useWebsocketContext } from "@/app/context/AuthContext";

const Page = ({ duration, orderNumber }) => {
  const { connection, sendMessage } = useWebsocketContext();
  const [time, setTime] = useState(duration || 0);
  const [stopTimer, setStopTimer] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [timerIncreased, setTimerIncreased] = useState(false);

  const handleTimeIncrease = () => {
    const payload = {
      action: "increase_trade_time",
      order_id: orderNumber,
    };
    sendMessage(JSON.stringify(payload));
    setShowConfirmation(false);
    setTimerIncreased(true);
  };

  const handleTimeReject = () => {
    setShowConfirmation(false);
  };

  const getFormattedTime = (milliseconds) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const totalMinutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    const minutes = totalMinutes % 60;
    return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;
  };

  // Combined timer and time check effect
  useEffect(() => {
    if (!duration || stopTimer) return;

    setTime(duration);
    const timer = setInterval(() => {
      setTime((prev) => {
        const newTime = prev - 1000;

        // Check for timer increase prompt
        if (newTime === 60000 && !timerIncreased) {
          setShowConfirmation(true);
        }

        // Check for timer completion
        if (newTime <= 0) {
          clearInterval(timer);
          setStopTimer(true);
          return 0;
        }

        return newTime;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [duration, stopTimer, timerIncreased]);

  return (
    <>
      <div className={styles.countDownTimer}>
        {!duration ? "expired" : getFormattedTime(time)}
      </div>

      {showConfirmation && (
        <div className={styles.alertPopup}>
          <p>
            Your order {orderNumber}: Do you want to increase trade time by 10
            minutes?
          </p>
          <div className={styles.btnContainer}>
            <button className={styles.yesBtn} onClick={handleTimeIncrease}>
              Yes
            </button>
            <button className={styles.noBtn} onClick={handleTimeReject}>
              No
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default Page;
