"use client";
import { useState, useRef, useEffect } from "react";
import styles from "./notificationList.module.css";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import { useWebsocketContext } from "@/app/context/AuthContext";
import { useRouter } from "next/navigation";

const NotificationItem = ({ msg, created_date, id, type, orderid, onRemove }) => {
  const router = useRouter();

  const { lastJsonMessage } = useWebsocketContext();

  const [isRemoving, setIsRemoving] = useState(false);

  useEffect(() => {
    if (lastJsonMessage?.error && orderid === lastJsonMessage?.data?.order_id) {
      // Use consistent toastId format for error messages
      const errorToastId = `error-${orderid}-${Date.now()}`;
      toast.warning(lastJsonMessage?.error, { toastId: errorToastId });
    }
    // Note: Success messages are handled by SSE in NotificationBox to prevent duplicates
    // if (lastJsonMessage?.action && orderid === lastJsonMessage?.data?.order_id) {
    //   toast.success(lastJsonMessage?.message);
    // }
  }, [lastJsonMessage, orderid]);

  const handleAcceptTradeMsg = async (orderid, id) => {
    const payload = {
      order_id: orderid,
    };

    try {
      const response = await customFetchWithToken.post(
        "/trade/accept-request/",
        payload
      );

      if (response.status === 200) {
        // Use consistent toastId format with SSEContext
        const toastId = `trade-${orderid}`;
        toast.success(response.data.message, {
          toastId,
          position: "top-right",
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true
        });
        setTimeout(() => {
          handleNotificationRemove(id);
          const params = new URLSearchParams({
            order_id: response.data.data.order_id,
            button: response.data.data.button,
            next_action: response.data.data.next_action,
            timeline_state: response.data.data.time_line_state,
            flag: response.data.data.flag,
            stepper: response.data.data.stepper,
            api: response.data.data.api,
          });
          router.push(`/pages/trade/${orderid}?${params.toString()}`);
        }, 2000);
      }
    } catch (error) {
      // Use consistent toastId format for error messages
      const errorToastId = `error-${orderid}-${Date.now()}`;
      toast.error(error.response?.data?.message || "An error occurred.", { 
        toastId: errorToastId 
      });
    }
  };

  const handleRejectTradeMsg = async (orderid) => {
    const payload = {
      order_id: orderid,
    };

    try {
      const response = await customFetchWithToken.post(
        "/trade/reject-request/",
        payload
      );
      if (response.status === 200) {
        // Use consistent toastId format with SSEContext
        const toastId = `trade-${orderid}`;
        toast.success(response.data.message, {
          toastId,
          position: "top-right",
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true
        });
        setTimeout(() => {
          handleNotificationRemove(id);
          router.push(`/pages/trade/${orderid}`);
        }, 2000);
      }
    } catch (error) {
      // Use consistent toastId format for error messages
      const errorToastId = `error-${orderid}-${Date.now()}`;
      toast.error(error.response?.data?.message || "An error occurred.", { 
        toastId: errorToastId 
      });
    }
  };

  const handleNotificationRemove = async (id) => {
    setIsRemoving(true);
    
    try {
      await customFetchWithToken.put(`/read-notification/?notification_id=${id}`);
      
      // Call the onRemove callback if provided (for parent state update)
      if (onRemove) {
        setTimeout(() => {
          onRemove(id);
        }, 500); // Corresponds to animation duration
      }
    } catch (error) {
      console.error(error);
      // Revert visibility if API call fails
      setIsRemoving(false);
      // Use consistent toastId format for error messages
      const errorToastId = `error-notification-${id}-${Date.now()}`;
      toast.error(error.response?.data?.message || "Failed to remove notification", {
        toastId: errorToastId
      });
    }
  };
  
  const formatDate = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    // return date.toLocaleString();
    return new Intl.DateTimeFormat('en-US', { 
      year: 'numeric', month: 'short', day: 'numeric', 
      hour: '2-digit', minute: '2-digit', hour12: true 
    }).format(date);
  };

  return (
    <div className={`${styles.container} ${isRemoving ? styles.remove : ""}`}>
      <div className={styles.icon}>
        {type === "trade_request" ? (
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"></path></svg>
        ) : (
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12.01" y2="8"></line></svg>
        )}
      </div>
      <div className={styles.content}>
        <div className={styles.message}>{msg}</div>
        <div className={styles.date}>{formatDate(created_date)}</div>
        {type === "trade_request" && !msg.includes("cancelled") && (
          <div className={styles.actions}>
            <button
              className={`${styles.button} ${styles.accept}`}
              onClick={() => handleAcceptTradeMsg(orderid, id)}
            >
              Accept
            </button>
            <button
              className={`${styles.button} ${styles.reject}`}
              onClick={() => handleRejectTradeMsg(orderid)}
            >
              Reject
            </button>
          </div>
        )}
      </div>
       <button
        onClick={() => handleNotificationRemove(id)}
        className={styles.closeButton}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          width="18"
          height="18"
        >
          <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"></path>
        </svg>
      </button>
    </div>
  );
};

export default NotificationItem;
